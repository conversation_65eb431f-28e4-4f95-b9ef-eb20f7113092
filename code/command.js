async function processInlineComments(projectId, mrIid, review, mrData) {
    try {
        const comments = JSON.parse(review);
        if (!Array.isArray(comments)) {
            logError('📝💥 Formato de comentários inválido. Esperado um array JSON.');
            return;
        }

        if (comments.length === 0) {
            logSuccess('🎉 Nenhum comentário inline foi gerado pela IA. O código parece estar em bom estado!');
            return;
        }

        logInfo(`📝 Enviando ${comments.length} comentário(s) inline...`);
        
        for (const comment of comments) {
            if (!comment.comment || !comment.position) {
                logWarning('📝⚠️ Comentário mal formatado encontrado, ignorando...');
                continue;
            }
            
            const position = {
                base_sha: mrData.diff_refs.base_sha,
                start_sha: mrData.diff_refs.start_sha,
                head_sha: mrData.diff_refs.head_sha,
                position_type: 'text',
                new_path: comment.position.new_path,
                new_line: comment.position.new_line
            };
            
            try {
                await postMRLineComments(projectId, mrIid, comment.comment, position);
                logSuccess(`✅ Comentário adicionado na linha ${comment.position.new_line} do arquivo ${comment.position.new_path}`);
            } catch (error) {
                logError(`❌ Erro ao adicionar comentário na linha ${comment.position.new_line} do arquivo ${comment.position.new_path}:`, error.message);
            }
        }
        
        console.log('\n🎉 Todos os comentários inline foram enviados para o GitLab!');
    } catch (error) {
        console.error('Erro ao processar comentários inline:', error.message);
        console.log('Enviando como comentário geral...');
        await postMRComments(projectId, mrIid, review);
    }
}

const inquirer = require('inquirer');
const autocomplete = require('inquirer-autocomplete-prompt');
inquirer.registerPrompt('autocomplete', autocomplete);
const { getGitlabProjects, getOpenMergeRequests, getMRDiff, getMergeRequestCommits, getCommitDiff } = require('../git/gitlab');
const { getAICodeReview } = require('../ai/api');
const { getValue, setValue } = require('../util/storage');
const { postMRComments, postMRLineComments, getMergeRequestData } = require('../git/gitlab');
const { searchIssues, getIssueWithWeblinks } = require('../jira/service');
const { logSuccess, logError, logWarning, logInfo, logCode, logAI } = require('../util/log');
const ora = require('ora');
const emoji = require('node-emoji');
const open = require('open');
const fs = require('fs');
const path = require('path');
const { root } = require('../util/path');

async function addCodeCommand(program) {
    const c = program
        .command('code')
        .alias('c')
        .description('Comandos para codigo: reviews de codigo');

    c.command('review')
        .alias('r')
        .description('Faz um code review de um MR utilizando Inteligencia Artificial')
        .action(async () => {
            try {
                // Perguntar como deseja encontrar o merge request
                const { searchOption } = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'searchOption',
                        message: 'Como deseja encontrar o merge request para review?',
                        choices: [
                            'Buscar por issue do Jira',
                            'Buscar por projeto GitLab'
                        ],
                        default: 'Buscar por issue do Jira'
                    },
                ]);

                let selectedProjectId, selectedMrIid, mrReviewData, mergeRequestLinks = [];

                if (searchOption === 'Buscar por issue do Jira') {
                    // Verificar configurações do Jira
                    const jiraEmail = getValue('jira.email');
                    const jiraToken = getValue('jira.token');
                    if (!jiraEmail || !jiraToken) {
                        logError('📋💥 Configuração do Jira não encontrada. Use "pacto jira credenciais" para configurar.');
                        return;
                    }

                    const jiraFilters = getValue('jira.filters');
                    if (!jiraFilters || !jiraFilters.jql) {
                        logError('🔍💥 Nenhuma configuração de busca encontrada. Use "pacto jira config-busca" para configurar.');
                        return;
                    }

                    // Buscar issues no Jira
                    const loadingJiraIssues = ora({
                        text: `${emoji.get('mag')} Buscando issues no Jira...`,
                        color: 'yellow'
                    }).start();

                    console.log(`Realizando busca no Jira com o filtro: ${jiraFilters.jql}`);
                    const issues = await searchIssues(jiraFilters.jql);
                    loadingJiraIssues.succeed(`${emoji.get('white_check_mark')} Issues encontradas!`);

                    if (issues.length === 0) {
                        console.log('Nenhuma atividade encontrada no Jira.');
                        return;
                    }

                    // Selecionar issue
                    const { selectedIssue } = await inquirer.prompt([
                        {
                            type: 'autocomplete',
                            name: 'selectedIssue',
                            message: 'Selecione uma atividade:',
                            source: (answersSoFar, input) => {
                                input = input || '';
                                return new Promise((resolve) => {
                                    const filteredIssues = issues.filter(issue =>
                                        issue.fields.summary.toLowerCase().includes(input.toLowerCase()) ||
                                        issue.key.toLowerCase().includes(input.toLowerCase())
                                    );
                                    resolve(filteredIssues.map(issue => ({
                                        name: `${issue.key} -> ${issue.fields.summary}`,
                                        value: issue.key,
                                    })));
                                });
                            },
                        },
                    ]);

                    // Buscar weblinks da issue
                    const loadingWeblinks = ora({
                        text: `${emoji.get('mag')} Buscando weblinks da issue...`,
                        color: 'yellow'
                    }).start();

                    const weblinks = await getIssueWithWeblinks(selectedIssue);
                    const gitlabMergeRequests = weblinks.filter(link =>
                        link.url.includes('gitlab') &&
                        link.url.includes('merge_requests')
                    );
                    loadingWeblinks.succeed(`${emoji.get('white_check_mark')} Weblinks encontrados!`);

                    if (!gitlabMergeRequests || gitlabMergeRequests.length === 0) {
                        console.log('Nenhum Merge Request do GitLab encontrado em Web links para esta atividade.');
                        return;
                    }

                    // Se houver múltiplos MRs, permitir seleção
                    let selectedMergeRequests = [];
                    if (gitlabMergeRequests.length === 1) {
                        selectedMergeRequests = [gitlabMergeRequests[0]];
                    } else {
                        // Criar choices para seleção individual ou todos
                        const choices = [
                            { name: `🚀 Revisar TODOS os ${gitlabMergeRequests.length} merge requests`, value: 'all' },
                            ...gitlabMergeRequests.map((mr, index) => ({
                                name: `${mr.title || `MR ${index + 1}`}: ${mr.url}`,
                                value: mr
                            }))
                        ];
                        
                        const { selectedMR } = await inquirer.prompt([
                            {
                                type: 'list',
                                name: 'selectedMR',
                                message: 'Selecione o(s) merge request(s) para review:',
                                choices: choices
                            }
                        ]);
                        
                        if (selectedMR === 'all') {
                            selectedMergeRequests = gitlabMergeRequests;
                        } else {
                            selectedMergeRequests = [selectedMR];
                        }

                        mergeRequestLinks = selectedMergeRequests.map(mr => mr.url);
                    }

                    // Processar multiple MRs se necessário
                    const mrReviewData = [];
                    
                    for (const mergeRequest of selectedMergeRequests) {
                        // Extrair project ID e MR IID da URL
                        const urlParts = mergeRequest.url.split('/-/merge_requests/');
                        const projectPath = urlParts[0].replace('https://gitlab.com/', '');
                        const mrIid = urlParts[1];

                        // Extrair apenas o nome do projeto (última parte após a barra)
                        const projectName = projectPath.split('/').pop();

                        // Buscar o project ID
                        const loadingProjects = ora({
                            text: `${emoji.get('mag')} Buscando informações do projeto ${projectName}...`,
                            color: 'yellow'
                        }).start();

                        const projects = await getGitlabProjects();
                        let project = null;
                        for (const p of projects) {
                            const projectUrl = p.url.replace(/\/$/, '');
                            const pPathFromUrl = projectUrl.replace('https://gitlab.com/', '');
                            if (pPathFromUrl === projectPath) {
                                project = p;
                                break;
                            }
                        }
                        
                        if (!project) {
                            loadingProjects.fail(`${emoji.get('x')} Projeto ${projectName} não encontrado no GitLab`);
                            console.error(`Projeto ${projectName} não encontrado. Pulando este MR.`);
                            continue;
                        }
                        
                        loadingProjects.succeed(`${emoji.get('white_check_mark')} Projeto encontrado: ${project.fullName}`);
                        
                        mrReviewData.push({
                            projectId: project.id,
                            projectName: project.fullName,
                            mrIid: mrIid,
                            url: mergeRequest.url,
                            title: mergeRequest.title
                        });
                    }
                    
                    if (mrReviewData.length === 0) {
                        console.error('Nenhum projeto válido encontrado. Abortando.');
                        return;
                    }
                    
                    // Se só temos um MR, usar o fluxo normal
                    if (mrReviewData.length === 1) {
                        selectedProjectId = mrReviewData[0].projectId;
                        selectedMrIid = mrReviewData[0].mrIid;
                    } else {
                        // Para múltiplos MRs, processar todos
                        await processMultipleMRReviews(mrReviewData);
                        return;
                    }

                } else {
                    // Busca tradicional por projeto GitLab
                    const loadingProjects = ora({
                        text: `${emoji.get('mag')} Buscando projetos no gitlab...`,
                        color: 'yellow'
                    }).start();

                    const projects = await getGitlabProjects();
                    loadingProjects.succeed(`${emoji.get('white_check_mark')} Projetos encontrados!`);

                    const defaultSelectedProjectId = getValue('code:default_selected_project_id');

                    const questions = [
                        {
                            type: 'autocomplete',
                            name: 'selected_project_id',
                            message: 'Selecione o projeto gitlab para review:',
                            default: defaultSelectedProjectId,
                            source: (answersSoFar, input) => {
                                input = input || '';
                                return new Promise((resolve) => {
                                    const filteredProjects = projects.filter(project =>
                                        project.fullName.toLowerCase().includes(input.toLowerCase())
                                    );
                                    resolve(filteredProjects.map(project => ({
                                        name: project.fullName,
                                        value: project.id
                                    })));
                                });
                            }
                        }
                    ];

                    const { selected_project_id } = await inquirer.prompt(questions);
                    selectedProjectId = selected_project_id;
                    setValue('code:default_selected_project', selected_project_id);

                    // Buscar merge requests
                    const loadingMergeRequests = ora({
                        text: `${emoji.get('mag')} Buscando merge requests...`,
                        color: 'yellow'
                    }).start();

                    const mergeRequests = await getOpenMergeRequests(selected_project_id);
                    loadingMergeRequests.succeed(`${emoji.get('white_check_mark')} Merge requests encontrados!`);

                    const defaultSelectedMRIid = getValue(`code:default_selected_mr_id_${selected_project_id}`);
                    const { selected_mr_iid } = await inquirer.prompt([{
                        type: 'autocomplete',
                        name: 'selected_mr_iid',
                        message: 'Selecione o merge request:',
                        default: defaultSelectedMRIid,
                        source: (answersSoFar, input) => {
                            input = input || '';
                            return new Promise((resolve) => {
                                const filteredMRs = mergeRequests.filter(mr =>
                                    `#${mr.iid}: ${mr.title}`.toLowerCase().includes(input.toLowerCase())
                                );
                                resolve(filteredMRs.map(mr => ({
                                    name: `#${mr.iid}: ${mr.title}`,
                                    value: mr.iid
                                })));
                            });
                        }
                    }]);
                    selectedMrIid = selected_mr_iid;
                    mergeRequestLinks = mergeRequests.filter(mr => mr.iid === selectedMrIid).map(mr => mr.web_url);
                    setValue(`code:default_selected_mr_id_${selected_project_id}`, selected_mr_iid);
                }

                // Buscar dados do merge request
                const loadingMr = ora({
                    text: `${emoji.get('mag')} Buscando dados do merge request...`,
                    color: 'yellow'
                }).start();
                
                const mrData = await getMergeRequestData(selectedProjectId, selectedMrIid);
                loadingMr.succeed(`${emoji.get('white_check_mark')} Dados do MR encontrados!`);                

                const loadingDiff = ora({
                    text: `${emoji.get('mag')} Buscando diff do merge request...`,
                    color: 'yellow'
                }).start();

                const diff = await getMRDiff(selectedProjectId, selectedMrIid);
                loadingDiff.succeed(`${emoji.get('white_check_mark')} Diff encontrado!`);

                // Perguntar o tipo de review primeiro
                const { reviewType } = await inquirer.prompt([{
                    type: 'list',
                    name: 'reviewType',
                    message: 'Que tipo de review deseja gerar?',
                    choices: [
                        { name: 'Review inline (comentários nas linhas específicas)', value: 'inline' },
                        { name: 'Review geral (formato tradicional)', value: 'general' }
                    ],
                    default: 'inline'
                }]);

                const isInlineMode = reviewType === 'inline';

                const defaultReviewMode = getValue('code:default_review_mode');
                const { reviewMode } = await inquirer.prompt([{
                    type: 'list',
                    name: 'reviewMode',
                    message: 'Qual modo de review deseja gerar?',
                    choices: [
                        { name: 'Resumo conciso (focado em erros)', value: 'straightforward' },
                        { name: 'Análise detalhada (foco em qualidade de código)', value: 'detailed' }
                    ],
                    default: defaultReviewMode | 'straightforward'
                }]);

                setValue('code:default_review_mode', reviewMode);
                
                const loadingReview = ora({
                    text: `${emoji.get('coffee')} ${emoji.get('robot_face')} AI trabalhando no review do codigo...`,
                    color: 'yellow'
                }).start();
                const review = await getAICodeReview(diff, null, mrData, isInlineMode, selectedProjectId, defaultReviewMode);
                loadingReview.succeed(`${emoji.get('white_check_mark')} Review concluido!`);

                console.log('\nResultados do Code Review:');
                console.log('-------------------');
                console.log(review);


                let currentReview = review;
                let action = '';

                do {
                    const response = await inquirer.prompt([{
                        type: 'list',
                        name: 'action',
                        message: 'O que você deseja fazer com o review?',
                        choices: [
                            { name: 'Enviar review para o merge request', value: 'send' },
                            { name: 'Fazer observações adicionais', value: 'edit' }
                        ]
                    }]);
                    
                    action = response.action;

                    if (action === 'edit') {
                        const { observations } = await inquirer.prompt([{
                            type: 'input',
                            name: 'observations',
                            message: 'Digite suas observações adicionais:'
                        }]);

                        const loadingNewReview = ora({
                            text: `${emoji.get('coffee')} ${emoji.get('robot_face')} AI trabalhando no review atualizado...`,
                            color: 'yellow'
                        }).start();
                        currentReview = await getAICodeReview(diff, observations, mrData, isInlineMode);
                        loadingNewReview.succeed(`${emoji.get('white_check_mark')} Review atualizado concluído!`);

                        console.log('\nCode Review Results (with observations):');
                        console.log('-------------------');
                        console.log(currentReview);
                    }
                } while (action !== 'send');

                if (isInlineMode) {
                    await processInlineComments(selectedProjectId, selectedMrIid, currentReview, mrData);
                } else {
                    await postMRComments(selectedProjectId, selectedMrIid, currentReview);
                    logSuccess('🎉 Este review foi enviado para o gitlab como comentário geral no merge request.');
                }
                
                mergeRequestLinks.forEach( async (link) => {
                    await open(link+'/diffs');
                })

            } catch (err) {
                logError(`💥 Erro durante o code review: ${err}`);
            }
        });

    c.command('merge-request-clear')
        .alias('mrc')
        .description('Gera patches dos commits de um merge request (excluindo merges)')
        .action(async () => {
            try {
                await handleMergeRequestClear();
            } catch (err) {
                logError(`💥 Erro durante a geração de patches: ${err}`);
            }
        });
}

async function processMultipleMRReviews(mrReviewData) {
    console.log(`\n🔄 Processando ${mrReviewData.length} merge requests...\n`);
    
    // Perguntar o tipo de review uma vez para todos os MRs
    const { reviewType } = await inquirer.prompt([{
        type: 'list',
        name: 'reviewType',
        message: 'Que tipo de review deseja gerar para todos os MRs?',
        choices: [
            { name: 'Review inline (comentários nas linhas específicas)', value: 'inline' },
            { name: 'Review geral (formato tradicional)', value: 'general' }
        ],
        default: 'inline'
    }]);

    const isInlineMode = reviewType === 'inline';
    
    for (let i = 0; i < mrReviewData.length; i++) {
        const mrData = mrReviewData[i];
        const { projectId, projectName, mrIid, url, title } = mrData;
        
        console.log(`\n📋 [${i + 1}/${mrReviewData.length}] Processando MR: ${title || `MR #${mrIid}`}`);
        console.log(`🔗 URL: ${url}`);
        console.log(`📁 Projeto: ${projectName}`);
        
        try {
            // Buscar dados do merge request
            const loadingMr = ora({
                text: `${emoji.get('mag')} Buscando dados do merge request...`,
                color: 'yellow'
            }).start();
            
            const mrRequestData = await getMergeRequestData(projectId, mrIid);
            loadingMr.succeed(`${emoji.get('white_check_mark')} Dados do MR encontrados!`);

            const loadingDiff = ora({
                text: `${emoji.get('mag')} Buscando diff do merge request...`,
                color: 'yellow'
            }).start();

            const diff = await getMRDiff(projectId, mrIid);
            loadingDiff.succeed(`${emoji.get('white_check_mark')} Diff encontrado!`);
            
            // Verificar se há mudanças para revisar
            if (!diff || diff.length === 0) {
                console.log(`⚠️  Nenhuma mudança encontrada neste MR. Pulando...`);
                continue;
            }

            const loadingReview = ora({
                text: `${emoji.get('coffee')} ${emoji.get('robot_face')} AI trabalhando no review do codigo...`,
                color: 'yellow'
            }).start();
            
            const review = await getAICodeReview(diff, null, mrRequestData, isInlineMode);
            loadingReview.succeed(`${emoji.get('white_check_mark')} Review concluído!`);

            // Enviar review
            if (isInlineMode) {
                await processInlineComments(projectId, mrIid, review, mrRequestData);
            } else {
                await postMRComments(projectId, mrIid, review);
                logSuccess('✅ Review enviado como comentário geral no merge request.');
            }

            logSuccess(`🎉 Review do MR #${mrIid} concluído com sucesso!`);

            // Abrir o merge request no navegador
            logInfo(`\n🔗 Abrindo merge request no navegador: ${url}`);
            await open(url);
            
        } catch (error) {
            console.error(`❌ Erro ao processar MR #${mrIid}:`, error.message);
            console.log('⏭️  Continuando para o próximo MR...');
        }
        
        // Pequena pausa entre MRs para evitar rate limiting
        if (i < mrReviewData.length - 1) {
            console.log('⏳ Aguardando antes do próximo MR...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    console.log(`\n🏁 Processamento concluído! ${mrReviewData.length} merge request(s) foram revisados.`);
}

async function handleMergeRequestClear() {
    // Seleção do projeto
    const loadingProjects = ora({
        text: `${emoji.get('mag')} Buscando projetos do GitLab...`,
        color: 'yellow'
    }).start();

    const projects = await getGitlabProjects();
    loadingProjects.succeed(`${emoji.get('white_check_mark')} ${projects.length} projetos encontrados!`);

    const { selectedProject } = await inquirer.prompt([
        {
            type: 'autocomplete',
            name: 'selectedProject',
            message: 'Selecione o repositório:',
            pageSize: 15,
            source: (answersSoFar, input) => {
                input = input || '';
                return new Promise((resolve) => {
                    const filteredProjects = projects.filter(project =>
                        project.fullName.toLowerCase().includes(input.toLowerCase()) ||
                        (project.description && project.description.toLowerCase().includes(input.toLowerCase()))
                    );
                    resolve(filteredProjects.map(project => ({
                        name: `${project.fullName} - ${project.description || 'Sem descrição'}`,
                        value: project
                    })));
                });
            }
        }
    ]);

    logInfo(`📁 Projeto selecionado: ${selectedProject.fullName}`);

    // Buscar merge requests do projeto
    const loadingMRs = ora({
        text: `${emoji.get('mag')} Buscando merge requests abertos...`,
        color: 'yellow'
    }).start();

    const mergeRequests = await getOpenMergeRequests(selectedProject.id);
    loadingMRs.succeed(`${emoji.get('white_check_mark')} ${mergeRequests.length} merge requests encontrados!`);

    if (mergeRequests.length === 0) {
        logWarning('⚠️  Nenhum merge request aberto encontrado neste projeto.');
        return;
    }

    // Seleção do merge request
    const { selectedMR } = await inquirer.prompt([
        {
            type: 'autocomplete',
            name: 'selectedMR',
            message: 'Selecione o merge request:',
            pageSize: 15,
            source: (answersSoFar, input) => {
                input = input || '';
                return new Promise((resolve) => {
                    const filteredMRs = mergeRequests.filter(mr =>
                        mr.title.toLowerCase().includes(input.toLowerCase()) ||
                        mr.source_branch.toLowerCase().includes(input.toLowerCase()) ||
                        mr.target_branch.toLowerCase().includes(input.toLowerCase()) ||
                        mr.iid.toString().includes(input)
                    );
                    resolve(filteredMRs.map(mr => ({
                        name: `!${mr.iid} - ${mr.title} (${mr.source_branch} → ${mr.target_branch})`,
                        value: mr
                    })));
                });
            }
        }
    ]);

    logInfo(`🔀 Merge request selecionado: !${selectedMR.iid} - ${selectedMR.title}`);

    // Buscar commits do merge request
    const loadingCommits = ora({
        text: `${emoji.get('mag')} Buscando commits do merge request...`,
        color: 'yellow'
    }).start();

    const allCommits = await getMergeRequestCommits(selectedProject.id, selectedMR.iid);

    // Filtrar commits que não são merges
    const nonMergeCommits = allCommits.filter(commit => {
        return commit.message.indexOf("Merge branch '") === -1;
    });

    loadingCommits.succeed(`${emoji.get('white_check_mark')} ${nonMergeCommits.length} commits encontrados (${allCommits.length - nonMergeCommits.length} merges excluídos)!`);

    if (nonMergeCommits.length === 0) {
        logWarning('⚠️  Nenhum commit não-merge encontrado neste merge request.');
        return;
    }

    // Ordenar commits do mais antigo para o mais novo
    nonMergeCommits.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Criar diretórios tmp e tmp/patches
    const tmpDir = path.join(root(), 'tmp');
    const patchesDir = path.join(tmpDir, 'patches');

    if (!fs.existsSync(tmpDir)) {
        fs.mkdirSync(tmpDir, { recursive: true });
    }

    if (!fs.existsSync(patchesDir)) {
        fs.mkdirSync(patchesDir, { recursive: true });
    }else{
        fs.rmSync(patchesDir, { recursive: true, force: true });
        fs.mkdirSync(patchesDir, { recursive: true });
    }

    // Gerar arquivo commits.txt
    const commitsFilePath = path.join(tmpDir, 'commits.txt');
    const commitsContent = nonMergeCommits.map(commit =>
        `${commit.id} - ${commit.title} (${commit.author_name} - ${commit.created_at})`
    ).join('\n');

    fs.writeFileSync(commitsFilePath, commitsContent, 'utf8');
    logSuccess(`📝 Arquivo de commits criado: ${commitsFilePath}`);

    // Gerar patches para cada commit
    const loadingPatches = ora({
        text: `${emoji.get('gear')} Gerando patches dos commits...`,
        color: 'yellow'
    }).start();

    let patchCount = 0;
    for (let i = 0; i < nonMergeCommits.length; i++) {
        const commit = nonMergeCommits[i];
        const patchNumber = String(i + 1).padStart(4, '0');
        const patchFileName = `${patchNumber}-${commit.id.substring(0, 8)}.patch`;
        const patchFilePath = path.join(patchesDir, patchFileName);

        try {
            const commitDiff = await getCommitDiff(selectedProject.id, commit.id);

            // Formatar o patch no formato git
            let patchContent = `From ${commit.id} Mon Sep 17 00:00:00 2001\n`;
            patchContent += `From: ${commit.author_name} <${commit.author_email}>\n`;
            patchContent += `Date: ${commit.created_at}\n`;
            patchContent += `Subject: [PATCH ${patchNumber}/${String(nonMergeCommits.length).padStart(4, '0')}] ${commit.title}\n\n`;
            patchContent += `${commit.message}\n`;
            patchContent += `---\n`;

            // Adicionar estatísticas dos arquivos
            const changedFiles = commitDiff.length;
            let insertions = 0;
            let deletions = 0;

            commitDiff.forEach(file => {
                const lines = file.diff.split('\n');
                lines.forEach(line => {
                    if (line.startsWith('+') && !line.startsWith('+++')) insertions++;
                    if (line.startsWith('-') && !line.startsWith('---')) deletions++;
                });
            });

            patchContent += ` ${changedFiles} file${changedFiles !== 1 ? 's' : ''} changed`;
            if (insertions > 0) patchContent += `, ${insertions} insertion${insertions !== 1 ? 's' : ''}(+)`;
            if (deletions > 0) patchContent += `, ${deletions} deletion${deletions !== 1 ? 's' : ''}(-)`;
            patchContent += `\n\n`;

            // Adicionar diffs dos arquivos
            commitDiff.forEach(file => {
                patchContent += `diff --git a/${file.old_path} b/${file.new_path}\n`;
                if (file.new_file) {
                    patchContent += `new file mode 100644\n`;
                    patchContent += `index 0000000..${file.new_path.substring(0, 7)}\n`;
                } else if (file.deleted_file) {
                    patchContent += `deleted file mode 100644\n`;
                    patchContent += `index ${file.old_path.substring(0, 7)}..0000000\n`;
                } else if (file.renamed_file) {
                    patchContent += `similarity index ${Math.round((1 - file.diff.length / 1000) * 100)}%\n`;
                    patchContent += `rename from ${file.old_path}\n`;
                    patchContent += `rename to ${file.new_path}\n`;
                }
                patchContent += file.diff + '\n';
            });

            fs.writeFileSync(patchFilePath, patchContent, 'utf8');
            patchCount++;
        } catch (error) {
            logError(`❌ Erro ao gerar patch para commit ${commit.id.substring(0, 8)}: ${error.message}`);
        }
    }

    loadingPatches.succeed(`${emoji.get('white_check_mark')} Patches gerados com sucesso!`);

    // Exibir resultados
    logSuccess(`\n🎉 Processamento concluído!`);
    logInfo(`📊 Estatísticas:`);
    logInfo(`   • Total de commits processados: ${nonMergeCommits.length}`);
    logInfo(`   • Patches gerados: ${patchCount}`);
    logInfo(`   • Commits excluídos (merges): ${allCommits.length - nonMergeCommits.length}`);
    logInfo(`\n📁 Arquivos gerados:`);
    logInfo(`   • Lista de commits: ${commitsFilePath}`);
    logInfo(`   • Patches: ${patchesDir}`);

    // Exibir comandos para aplicar os patches
    logSuccess(`\n✨ Os patches estão prontos para uso! ${emoji.get('rocket')}`);
    logInfo(`\n🔧 Comandos para aplicar os patches no seu repositório:`);
    logCode(`\n# 1. Navegue até o diretório do seu repositório local`);
    logCode(`cd /caminho/para/seu/repositorio`);
    logCode(`\n# 2. Certifique-se de estar na branch correta (geralmente main ou master) e que ela esteja atualizada`);
    logCode(`git checkout master  # ou main`);
    logCode(`git pull`);
    logCode(`\n# 3. crie uma nova branch para aplicar os patches`);
    logCode(`git checkout -b <nova-branch>`);
    logCode(`\n# 4. Aplique todos os patches em ordem:`);
    logCode(`git am ${path.relative(process.cwd(), patchesDir)}/*.patch`);
    logCode(`\n# OU aplique patches individualmente:`);
    for (let i = 0; i < Math.min(3, patchCount); i++) {
        const patchNumber = String(i + 1).padStart(4, '0');
        const commitId = nonMergeCommits[i].id.substring(0, 8);
        logCode(`git am ${path.relative(process.cwd(), patchesDir)}/${patchNumber}-${commitId}.patch`);
    }
    if (patchCount > 3) {
        logCode(`# ... e assim por diante para os demais patches`);
    }
    logCode(`\n# 4. Verifique se tudo foi aplicado corretamente:`);
    logCode(`git log --oneline -${patchCount}`);
    logCode(`\n# 5. Se necessário, faça push das mudanças:`);
    logCode(`git push origin main  # ou master`);

    logWarning(`\n⚠️  Dicas importantes:`);
    logWarning(`   • Faça backup do seu repositório antes de aplicar os patches`);
    logWarning(`   • Se houver conflitos, resolva-os e use 'git am --continue'`);
    logWarning(`   • Para cancelar a aplicação de patches: 'git am --abort'`);
    logWarning(`   • Verifique se a branch de destino está atualizada antes de aplicar`);

    logInfo(`\n📋 Informações do Merge Request:`);
    logInfo(`   • Projeto: ${selectedProject.fullName}`);
    logInfo(`   • MR: !${selectedMR.iid} - ${selectedMR.title}`);
    logInfo(`   • Branch origem: ${selectedMR.source_branch}`);
    logInfo(`   • Branch destino: ${selectedMR.target_branch}`);
    logInfo(`   • URL: ${selectedMR.web_url}`);
}

module.exports = { addCodeCommand };
