const { exec, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const chalk = require('chalk');
const { isRunningInContainer } = require('../../util/container');
const { logSuccess, logError, logWarning, logInfo, logDeploy } = require('../../util/log.js');
const { getDockerComposeCommand, normalizeFilePath, executeDockerComposeCommand } = require('../../util/docker-compose.js');
const { resetServiceTagsToDefault } = require('./services.js');
const { getValue, setValue } = require('../../util/storage.js');

function getComposePath() {
    return normalizeFilePath(path.resolve(__dirname, 'docker-compose.yml'));
}

function isHostGatewaySupported() {
    try {
        // Tenta executar um comando docker que usa host-gateway para ver se é suportado
        execSync('docker run --rm --add-host=host.docker.internal:host-gateway alpine ping -c 1 host.docker.internal', { stdio: 'ignore' });
        return true;
    } catch (error) {
        return false;
    }
}

function isHostGatewayWorking() {
    let isHostGatewayWorking = getValue('deploy:is_host_gateway_working');
    // if(isHostGatewayWorking === undefined || isHostGatewayWorking === null || isHostGatewayWorking === ''){
        const checkComposeCheckPath = normalizeFilePath(path.resolve(__dirname, 'docker-compose.check.yml'));
        const composeCommand = getDockerComposeCommand();
        try {
            executeDockerComposeCommand(`${composeCommand} -p dev-tools-host-check -f "${checkComposeCheckPath}" up -d`, { stdio: 'ignore' });
            const logs = executeDockerComposeCommand(`${composeCommand} -p dev-tools-host-check -f "${checkComposeCheckPath}" logs telnet-client`);
            if(logs.indexOf('Connection refused') !== -1 || logs.indexOf("can't connect to remote host")){
                isHostGatewayWorking = false;
            }else{
                logInfo('✅ host-gateway está funcionando!');
                isHostGatewayWorking = true;
            }
            executeDockerComposeCommand(`${composeCommand} -f "${checkComposeCheckPath}" down`, { stdio: 'ignore' });
        } catch (error) {
            logInfo('Erro ao verificar se host-gateway está funcionando: 😵', error);
            logInfo('Provavelmente você está usando uma versão muito antiga do Docker. Vamos assumir que está funcionando... 😅🚀')
            isHostGatewayWorking = true;
        }
    // }

    setValue('deploy:is_host_gateway_working', isHostGatewayWorking);
    return isHostGatewayWorking;
}

function replaceHostGateway() {
    const composeFilePath = getComposePath();
    
    try {
        logInfo('🔄 Substituindo host-gateway por ********** no arquivo docker-compose.yml...');

        // Ler o conteúdo do arquivo
        let content = fs.readFileSync(composeFilePath, 'utf8');

        // Substituir todas as ocorrências de host-gateway por **********
        content = content.replace(/host-gateway/g, '**********');

        // Escrever o conteúdo modificado de volta no arquivo
        fs.writeFileSync(composeFilePath, content, 'utf8');

        logSuccess('✅ Substituição concluída com sucesso!');
    } catch (error) {
        logError(`❌ Erro ao substituir host-gateway: ${error.message}`);
        throw error;
    }
}

function removeExtraHosts(){
    const composeFilePath = getComposePath();
    try {
        logInfo('🔄 Removendo extra_hosts do arquivo docker-compose.yml...')
        const content = fs.readFileSync(composeFilePath, 'utf8');
        const newContent = content.replace(/extra_hosts:\s+- host.docker.internal:host-gateway/g, '');
        fs.writeFileSync(composeFilePath, newContent, 'utf8');
        logSuccess('✅ Extra_hosts removidos com sucesso!');
    } catch (error) {
        logError(`❌ Erro ao remover extra_hosts: ${error.message}`);
        throw error;
    };
}

async function deployCompose(selectedServices) {
    const composeFilePath = getComposePath();

    if (!isHostGatewaySupported()) {
        logWarning('⚠️ host-gateway não é suportado neste ambiente. Substituindo por **********...');
        replaceHostGateway();
    }

    
    if(!isHostGatewayWorking()){
        logWarning('⚠️ host-gateway não está funcionando. Estou removendo o extra_hosts do arquivo docker-compose.yml...');
        removeExtraHosts();
    }

    return new Promise((resolve, reject) => {
        const composeCommand = getDockerComposeCommand();
        const process = exec(`${composeCommand} -p dev-tools -f "${composeFilePath}" up -d ${selectedServices}`);

        process.stdout.on('data', (data) => {
            logInfo(`${data}`);
        });

        process.stderr.on('data', (data) => {
            logError(`${data}`);
        });

        process.on('close', (code) => {
            if (code !== 0) {
                reject(new Error(`Process exited with code ${code}`));
            } else {
                logDeploy('🚀 Deploy realizado com sucesso!');
                logInfo('⏳ Aguarde a inicialização dos serviços...');

                if(!isRunningInContainer()) {
                    exec('pacto n hd', (err, stdout, stderr) => {
                        if (err) {
                            logError(err);
                            reject(new Error(`Error: ${err.message}`));
                            return;
                        }else{
                            resolve();
                        }

                        logInfo(stdout);
                    });
                   
                }else{
                    resolve();
                }
            }
        });
    });
}

function resetBranchs() {
    try {
        const composeFilePath = getComposePath();
        const composeCommand = getDockerComposeCommand();

        logInfo('🔍 Verificando serviços em execução...');
        
        // Usa a nova função para executar comando com tratamento de erros específicos
        const runningServicesOutput = executeDockerComposeCommand(
            `${composeCommand} -p dev-tools -f "${composeFilePath}" ps --services`
        ).trim().replace(/\n/g, ' ');

        logInfo('🏷️ Resetando tags dos serviços para as tags padrão definidas nas labels...');
        const servicesUpdated = resetServiceTagsToDefault();
        if (servicesUpdated > 0) {
            logSuccess('📄 Tags dos serviços resetadas com sucesso para as tags padrão!');
        }

        if (runningServicesOutput) {
            logInfo('🔄 Reiniciando os serviços que estavam em execução...');
            executeDockerComposeCommand(
                `${composeCommand} -p dev-tools -f "${composeFilePath}" up -d ${runningServicesOutput}`,
                { stdio: 'inherit' }
            );
            logSuccess('🎉 Serviços reiniciados com sucesso!');
        }

    } catch (error) {
        logError('❌ Erro ao resetar as branchs e reiniciar os serviços:', error.message);
        // Se for um erro específico do Docker Desktop, mostra as soluções
        if (error.message.includes('Docker Desktop')) {
            logWarning('💡 Tente as soluções sugeridas acima antes de executar o comando novamente.');
        }
    }
}


module.exports = { deployCompose, resetBranchs, getComposePath };
