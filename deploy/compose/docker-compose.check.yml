services:
  server1:
    image: alpine
    command: sh -c "nc -l -p 49152"
    ports:
      - 49152:49152
    networks:
      - dev-tools-test-net
    extra_hosts:
      - host.docker.internal:host-gateway

  telnet-client:
    image: keviocastro/iputils
    command: "telnet host.docker.internal 49152"
    networks:
      - dev-tools-test-net
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - server1

networks:
  dev-tools-test-net:
    driver: bridge